<div class="t-w-full">
  <div
    id="content-filter-chart-container"
    class="t-bg-white t-border t-border-[#dcdcdc] t-flex t-gap-6 t-p-7 t-rounded t-flex-col">
    <div class="t-flex t-justify-between t-items-center t-w-full">
      <!-- <div
        class="t-flex t-flex-col md:t-flex-row t-items-start md:t-items-center t-gap-0 md:t-gap-6">
        <h3
          class="t-text-lg t-font-semibold t-text-primary t-max-h-[36px] t-overflow-visible t-z-10">
          Content Filter
        </h3>
        <div
          class="t-flex t-items-center t-cursor-pointer t-text-secondary"
          (click)="openFocusedSection()">
          <span class="t-text-xs t-font-medium">View Details</span>
          <button
            kendoButton
            [svgIcon]="svgOpenNew"
            title="Open in new tab"
            fillMode="flat"
            class="t-text-secondary"></button>
        </div>
      </div> -->
      <div class="t-flex t-space-x-2 t-items-center">
        <venio-title-and-download
          [title]="'Content Filter'"
          [dashboardType]="
            dashboardType.Inappropriate_Words
          "></venio-title-and-download>
      </div>
    </div>
    <div class="t-w-full t-min-h-[300px]">
      @if (shouldRenderChart()) {
      <plotly-plot
        id="content-filter-chart"
        [data]="graph.data"
        [layout]="graph.layout"
        [config]="config"></plotly-plot>
      } @else if (shouldShowSkeleton()) {
      <!-- Responsive bar chart skeleton -->
      <div class="t-w-full t-h-[300px] t-p-4">
        <div class="t-flex t-flex-col t-h-full">
          <!-- Chart title skeleton -->
          <div class="t-mb-4">
            <kendo-skeleton
              shape="text"
              [width]="'40%'"
              [height]="'1.25rem'"
              class="t-mx-auto"></kendo-skeleton>
          </div>

          <!-- Chart area -->
          <div
            class="t-flex-1 t-flex t-items-end t-justify-center t-gap-[2%] t-pb-8">
            <!-- Responsive bars using percentage widths -->
            <div
              class="t-flex t-flex-col t-items-center t-gap-2"
              style="width: 12%">
              <kendo-skeleton
                shape="rectangle"
                [width]="'100%'"
                [height]="'120px'"
                class="t-rounded-t"></kendo-skeleton>
              <kendo-skeleton
                shape="text"
                [width]="'100%'"
                [height]="'0.75rem'"></kendo-skeleton>
            </div>

            <div
              class="t-flex t-flex-col t-items-center t-gap-2"
              style="width: 12%">
              <kendo-skeleton
                shape="rectangle"
                [width]="'100%'"
                [height]="'80px'"
                class="t-rounded-t"></kendo-skeleton>
              <kendo-skeleton
                shape="text"
                [width]="'100%'"
                [height]="'0.75rem'"></kendo-skeleton>
            </div>

            <div
              class="t-flex t-flex-col t-items-center t-gap-2"
              style="width: 12%">
              <kendo-skeleton
                shape="rectangle"
                [width]="'100%'"
                [height]="'160px'"
                class="t-rounded-t"></kendo-skeleton>
              <kendo-skeleton
                shape="text"
                [width]="'100%'"
                [height]="'0.75rem'"></kendo-skeleton>
            </div>

            <div
              class="t-flex t-flex-col t-items-center t-gap-2"
              style="width: 12%">
              <kendo-skeleton
                shape="rectangle"
                [width]="'100%'"
                [height]="'100px'"
                class="t-rounded-t"></kendo-skeleton>
              <kendo-skeleton
                shape="text"
                [width]="'100%'"
                [height]="'0.75rem'"></kendo-skeleton>
            </div>

            <div
              class="t-flex t-flex-col t-items-center t-gap-2"
              style="width: 12%">
              <kendo-skeleton
                shape="rectangle"
                [width]="'100%'"
                [height]="'140px'"
                class="t-rounded-t"></kendo-skeleton>
              <kendo-skeleton
                shape="text"
                [width]="'100%'"
                [height]="'0.75rem'"></kendo-skeleton>
            </div>

            <div
              class="t-flex t-flex-col t-items-center t-gap-2"
              style="width: 12%">
              <kendo-skeleton
                shape="rectangle"
                [width]="'100%'"
                [height]="'90px'"
                class="t-rounded-t"></kendo-skeleton>
              <kendo-skeleton
                shape="text"
                [width]="'100%'"
                [height]="'0.75rem'"></kendo-skeleton>
            </div>
          </div>

          <!-- Y-axis label skeleton -->
          <div class="t-flex t-justify-between t-items-center t-mt-2">
            <kendo-skeleton
              shape="text"
              [width]="'15%'"
              [height]="'0.875rem'"></kendo-skeleton>
            <kendo-skeleton
              shape="text"
              [width]="'15%'"
              [height]="'0.875rem'"></kendo-skeleton>
          </div>
        </div>
      </div>
      } @else if (shouldShowEmptyState()) {
      <div
        class="t-flex t-items-center t-justify-center t-h-[300px] t-text-gray-600">
        <div class="t-text-center">
          <p class="t-text-lg t-mb-2">No content analysis available</p>
          <p class="t-text-sm t-text-gray-500">
            No inappropriate content detected
          </p>
        </div>
      </div>
      }
    </div>
  </div>
</div>
