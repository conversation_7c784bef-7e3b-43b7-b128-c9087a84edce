import { Component, computed, inject, PLATFORM_ID } from '@angular/core'
import { PlotlyModule } from 'angular-plotly.js'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade, ECADashboardType } from '@venio/data-access/ai'
import { hyperlinkOpenSmIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { isPlatformBrowser } from '@angular/common'
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component'
import { SkeletonModule } from '@progress/kendo-angular-indicators'

@Component({
  selector: 'venio-inappropriate-content',
  standalone: true,
  imports: [PlotlyModule, TitleAndDownloadComponent, SkeletonModule],
  templateUrl: './inappropriate-content.component.html',
  styleUrl: './inappropriate-content.component.scss',
})
export class InappropriateContentComponent {
  public dashboardType = ECADashboardType

  private readonly aiFacade = inject(AiFacade)

  private readonly platformId = inject(PLATFORM_ID)

  private readonly isBrowser = isPlatformBrowser(this.platformId)

  public readonly svgOpenNew: SVGIcon = hyperlinkOpenSmIcon

  // ECA Inappropriate Content API data
  public readonly inappropriateContentData = toSignal(
    this.aiFacade.selectEcaInappropriateContentSuccess$,
    { initialValue: null }
  )

  // Loading state
  public readonly isLoading = toSignal(
    this.aiFacade.selectEcaInappropriateContentLoading$,
    { initialValue: false }
  )

  public readonly barChartData = computed(() => {
    const apiData = this.inappropriateContentData()

    // Use ECA Inappropriate Content API data if available
    if (
      apiData?.data?.inappropriateItems &&
      Array.isArray(apiData.data.inappropriateItems)
    ) {
      return {
        categories: apiData.data.inappropriateItems.map(
          (item: any) => item.category
        ),
        values: apiData.data.inappropriateItems.map((item: any) => item.count),
      }
    }

    // Fallback to empty data
    return {
      categories: [],
      values: [],
    }
  })

  // Signal to determine if chart data is ready for rendering
  public readonly isChartDataReady = computed(() => {
    const data = this.barChartData()
    return (
      data &&
      data.categories &&
      Array.isArray(data.categories) &&
      data.categories.length > 0 &&
      data.values &&
      Array.isArray(data.values) &&
      data.values.length > 0
    )
  })

  // Signal to determine if chart should be rendered
  public readonly shouldRenderChart = computed(() => {
    return this.isBrowser && this.isChartDataReady() && !this.isLoading()
  })

  // Signal to determine if skeleton should be shown
  public readonly shouldShowSkeleton = computed(() => {
    return this.isLoading() || (!this.isChartDataReady() && !this.isLoading())
  })

  public openFocusedSection(): void {
    this.aiFacade.setEciFocusedSectionOpened(true)
  }

  public get graph(): any {
    const data = this.barChartData()
    const categories = data?.categories || []
    const values = data?.values || []

    return {
      data: [
        {
          x: categories,
          y: values,
          type: 'bar',
          marker: {
            color: '#6366f1',
          },
        },
      ],
      layout: {
        autosize: true,
        title: '',
        automargin: true,
        margin: { t: 20, r: 20, b: 60, l: 60 },
        showlegend: false,
        xaxis: {
          title: '',
          tickangle: -45,
        },
        yaxis: {
          title: 'Count',
        },
        plot_bgcolor: 'rgba(0,0,0,0)',
        paper_bgcolor: 'rgba(0,0,0,0)',
      },
    }
  }

  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'toImage',
      'sendDataToCloud',
      'editInChartStudio',
      'zoom2d',
      'select2d',
      'pan2d',
      'lasso2d',
      'autoScale2d',
      'resetScale2d',
    ],
  }
}
