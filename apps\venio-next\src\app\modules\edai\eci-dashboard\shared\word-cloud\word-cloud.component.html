<div
  class="t-bg-white t-border t-border-[#dcdcdc] t-flex t-gap-6 t-rounded t-flex-col t-h-full t-p-7">
  <venio-title-and-download
    [title]="'Relevant Keywords'"
    [dashboardType]="dashboardType.WordCloud"></venio-title-and-download>
  <div class="t-flex t-items-center t-justify-center t-flex-1">
    @if (shouldRenderWordCloud()) {
    <angular-tag-cloud
      [data]="data()"
      [width]="options.width"
      [height]="options.height"
      [overflow]="options.overflow">
    </angular-tag-cloud>
    } @else if (shouldShowSkeleton()) {
    <!-- Word cloud skeleton with varied sizes and positions -->
    <div
      class="t-relative t-w-full t-h-[400px] t-flex t-items-center t-justify-center">
      <div
        class="t-absolute t-inset-0 t-flex t-flex-wrap t-items-center t-justify-center t-gap-2 t-p-4">
        <!-- Large words (center) -->
        <kendo-skeleton
          shape="text"
          [width]="'8rem'"
          [height]="'2rem'"
          class="t-text-2xl t-mx-2"></kendo-skeleton>
        <kendo-skeleton
          shape="text"
          [width]="'6rem'"
          [height]="'1.75rem'"
          class="t-text-xl t-mx-1"></kendo-skeleton>

        <!-- Medium words -->
        <kendo-skeleton
          shape="text"
          [width]="'5rem'"
          [height]="'1.5rem'"
          class="t-text-lg t-mx-1"></kendo-skeleton>
        <kendo-skeleton
          shape="text"
          [width]="'7rem'"
          [height]="'1.5rem'"
          class="t-text-lg t-mx-1"></kendo-skeleton>
        <kendo-skeleton
          shape="text"
          [width]="'4rem'"
          [height]="'1.25rem'"
          class="t-text-base t-mx-1"></kendo-skeleton>

        <!-- Small words -->
        <kendo-skeleton
          shape="text"
          [width]="'3rem'"
          [height]="'1rem'"
          class="t-text-sm t-mx-1"></kendo-skeleton>
        <kendo-skeleton
          shape="text"
          [width]="'4.5rem'"
          [height]="'1rem'"
          class="t-text-sm t-mx-1"></kendo-skeleton>
        <kendo-skeleton
          shape="text"
          [width]="'3.5rem'"
          [height]="'1rem'"
          class="t-text-sm t-mx-1"></kendo-skeleton>
        <kendo-skeleton
          shape="text"
          [width]="'5.5rem'"
          [height]="'1.25rem'"
          class="t-text-base t-mx-1"></kendo-skeleton>

        <!-- Extra small words -->
        <kendo-skeleton
          shape="text"
          [width]="'2.5rem'"
          [height]="'0.875rem'"
          class="t-text-xs t-mx-1"></kendo-skeleton>
        <kendo-skeleton
          shape="text"
          [width]="'3rem'"
          [height]="'0.875rem'"
          class="t-text-xs t-mx-1"></kendo-skeleton>
        <kendo-skeleton
          shape="text"
          [width]="'2rem'"
          [height]="'0.875rem'"
          class="t-text-xs t-mx-1"></kendo-skeleton>
      </div>
    </div>
    } @else if (shouldShowEmptyState()) {
    <div
      class="t-flex t-items-center t-justify-center t-h-[400px] t-text-gray-500">
      <div class="t-text-center">
        <p class="t-text-lg t-mb-2">No keywords available</p>
        <p class="t-text-sm">Word cloud will appear when data is loaded</p>
      </div>
    </div>
    }
  </div>
</div>
