import { Component, inject, computed } from '@angular/core'
import {
  TagCloudComponent,
  CloudOptions,
  CloudData,
} from 'angular-tag-cloud-module'
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade, ECADashboardType } from '@venio/data-access/ai'
import { map } from 'rxjs'
import { SkeletonModule } from '@progress/kendo-angular-indicators'

@Component({
  selector: 'venio-word-cloud',
  standalone: true,
  imports: [TagCloudComponent, TitleAndDownloadComponent, SkeletonModule],
  templateUrl: './word-cloud.component.html',
  styleUrl: './word-cloud.component.scss',
})
export class WordCloudComponent {
  private readonly aiFacade = inject(AiFacade)

  public readonly dashboardType = ECADashboardType

  public readonly options: CloudOptions = {
    // if width is between 0 and 1 it will be set to the width of the upper element multiplied by the value
    width: 1000,
    // if height is between 0 and 1 it will be set to the height of the upper element multiplied by the value
    height: 400,
    overflow: false,
  }

  // ECA Word Cloud API data
  public readonly ecaWordCloudData = toSignal(
    this.aiFacade.selectEcaWordCloudSuccess$,
    { initialValue: null }
  )

  // Loading state
  public readonly isLoading = toSignal(
    this.aiFacade.selectEcaWordCloudLoading$,
    { initialValue: false }
  )

  public readonly data = toSignal(
    this.aiFacade.selectEcaWordCloudSuccess$.pipe(
      map((wordCloudResponse) => {
        try {
          // Use ECA Word Cloud API data if available
          if (
            wordCloudResponse?.data?.wordCloudItems &&
            Array.isArray(wordCloudResponse.data.wordCloudItems)
          ) {
            const transformed = this.#transformEcaWordCloudToCloudData(
              wordCloudResponse.data.wordCloudItems
            )
            return transformed || []
          }

          return []
        } catch (error) {
          console.error('Error processing word cloud data:', error)
          return []
        }
      })
    ),
    { initialValue: [] as CloudData[] }
  )

  // Signal to determine if word cloud should be rendered
  public readonly shouldRenderWordCloud = computed(() => {
    const data = this.data()
    return data && data.length > 0 && !this.isLoading()
  })

  // Signal to determine if skeleton should be shown
  public readonly shouldShowSkeleton = computed(() => {
    const data = this.data()
    return this.isLoading() || !data || data.length === 0
  })

  #transformEcaWordCloudToCloudData(wordCloudItems: any[]): CloudData[] {
    try {
      if (!wordCloudItems || !Array.isArray(wordCloudItems)) {
        console.warn('Invalid ECA word cloud data for transformation')
        return []
      }

      return wordCloudItems
        .map((item, index) => {
          if (!item || !item.text) return null

          const value = item.value || 0

          return {
            text: item.text,
            weight: Math.max(10, Math.min(100, value)), // Scale weight between 10-100
            color: this.#generateColor(index),
            tooltip: `${item.text}: ${value} occurrences`,
            link: '',
            external: false,
          } as CloudData
        })
        .filter((item) => item !== null)
    } catch (error) {
      console.error('Error transforming ECA word cloud to cloud data:', error)
      return []
    }
  }

  #generateColor(index: number): string {
    const colors = [
      '#FF6B6B',
      '#4ECDC4',
      '#45B7D1',
      '#96CEB4',
      '#FFEAA7',
      '#DDA0DD',
      '#98D8C8',
      '#F7DC6F',
      '#BB8FCE',
      '#85C1E9',
    ]
    return colors[index % colors.length]
  }
}
